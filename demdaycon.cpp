#include <bits/stdc++.h>
const int maxn =1e3;
const int mod = 1e9 + 7;
int s;
int a[maxn+5];
int dp[maxn+5][maxn+5];
int pre[maxn+5];
int n;
using namespace std;

int main()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    int q;
    cin >> n;
    for(int i = 1; i <= n; i++) cin >> a[i];
    dp[0][0] = 1;
    for(int i = 1; i <= n; i++)
    {
        for(int j = 0; j <= 1000; j++)
        {
                dp[i][j] = dp[i-1][j];
                if(j - a[i] >= 0)
                {
                    dp[i][j] = (dp[i-1][j] + dp[i-1][j-a[i]]) % mod;
                }

        }
    }
    cin >> q;
    pre[0] = 0;
    for(int i = 1; i <= 1000; i++)
    {
        pre[i] = (pre[i-1] + dp[n][i]) % mod;
    }
    while(q--)
    {
        int l,r;
        cin >> l >> r;
        int x = (pre[r] - pre[l-1] + mod) % mod;
        if(x < 0) x += mod;
        cout << x << endl;
    }

    return 0;
}