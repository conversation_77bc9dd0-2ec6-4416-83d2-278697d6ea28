#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int t;
    cin >> t;
    while(t--)
    {
        int n;
        cin >> n;
        vector<int> a(n);
        for(int i = 0; i < n; i++) cin >> a[i];
        string s;
        cin >> s;
        int q;
        cin >> q;
        vector<int> ans(q);
        while(q--)
        {
            int tp;
            cin >> tp;
            if(tp == 1)
            {
                int l,r;
                cin >> l >> r;
                for(int i = l; i <= r; i++)
                {
                    if(a[i] == 0) a[i] = 1;
                    else a[i] = 0;

                }
            }
            if(tp == 2)
            {
                char x;
                cin >> x;
                int cur = 0;
                for(int i = 0; i < s.size(); i++)
                {
                    if(s[i] == x)
                    {
                        cur = cur ^ a[i];
                    }
                }
                vector<int> ans.push_back(cur);
            }
            for(auto &i : ans) cout << i << " ";
            cout << endl;
        }
    }
    return 0;
}