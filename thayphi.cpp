#include <bits/stdc++.h>
using namespace std;

vector<int> primes;

void sieve(int limit = 1000000) 
{
    vector<bool> isPrime(limit+1, true);
    isPrime[0] = isPrime[1] = false;
    for (int i = 2; i * i <= limit; i++) {
        if (isPrime[i]) {
            for (int j = i*i; j <= limit; j += i)
                isPrime[j] = false;
        }
    }
    for (int i = 2; i <= limit; i++)
        if (isPrime[i]) primes.push_back(i);
}

int main() 
{
    ios::sync_with_stdio(0);
    cin.tie(0);

    sieve();

    long long n;
    cin >> n;
    for(int i = 1; i <= sqrt(n); i++)
    {
        int somu = 1;
        int cur = 1;
        while(cur < n)
        {
            cur = pow(primes[i], somu);
            somu++;
        }
        if(cur == n)
        {
            cout  << primes[i] << " " << somu - 1;
            return 0;
        }
    }
    cout << 0 << " " << 0;

    return 0;
}
