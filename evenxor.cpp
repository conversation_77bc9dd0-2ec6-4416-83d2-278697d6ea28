#include<bits/stdc++.h>

using namespace std;



void solve()
{
    int tc;
    cin >> tc;
    while (tc--) {
        int n;
        cin >> n;

        if (n & 1)
         {
            int x = 2;
            for (int i = 1; i <= n - 1; i++) {
                cout << x << " ";
                x++;
            }
            cout << 300000 << endl;
            for (int i = 1; i <= n - 1; i++) {
                cout << x << " ";
                x++;
            }
            cout << 299999 << endl;
        } 
        else 
        {
            int x = 2;
            for (int i = 1; i <= n; i++) {
                cout << x << " ";
                x++;
            }
            cout << endl;
            for (int i = 1; i <= n; i++) {
                cout << x << " ";
                x++;
            }
            cout << endl;
        }
    }
}

int main()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);

    solve();
    return 0;
}