#include <bits/stdc++.h>
#define endl '\n'
int a[1005];
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}


string toBinary(int num)
{
    

    string binary = "";
    while (num > 0) {
        binary = to_string(num % 2) + binary;
        num /= 2;
    }
    return binary;
}

int main()
{
    init();
    int n,m,k;
    cin >> n >> m >> k;
    for(int i = 1;  i <= m +1; i++)
    {
        cin >> a[i];
    }
    bool found = false;
    int ans = 0;
    for(int i = 1; i < m +1; i++)
    {
        int count = 0;
        int diff = a[i] ^ a[m+1];
        string difff = toBinary(diff);
        for(auto &s : difff)
        {
            if(s == '1') count++;
        }
        if(count <= k)
        {
            found = true;
            ans++;
        }

    }
    cout << ans;
    return 0;
}