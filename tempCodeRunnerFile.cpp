#include <bits/stdc++.h>
#define endl '\n'
const int limit = 1e8;
bool isPrime[limit + 1];
using namespace std;
void sieve() 
{
    memset(isPrime, true, sizeof(isPrime));
    isPrime[0] = isPrime[1] = false;
    for (int i = 2; i * i <= limit; i++) {
        if (isPrime[i]) {
            for (int j = i*i; j <= limit; j += i)
                isPrime[j] = false;
        }
    }
}
void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    sieve();
    int cnt = 0;
    for(int i = 2; i <= limit; i++)
    {    
        if(isPrime[i]) 
        {
            cnt++;
            if(cnt % 100 == 0) cout << i << endl;
        }
    }
    return 0;
}