#include <bits/stdc++.h>
#define endl '\n'    
using namespace std;

const int MAXN = 100000 + 5;
int n, q;
long long a[MAXN];
vector<long long> f; 
vector<long long> s; 

void init() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main() {
    init();
    cin >> n >> q;
    f.resize(3*n + 5);
    s.resize(3*n + 5);

    for (int i = 1; i <= n; i++) 
    {
        cin >> a[i];
        f[i] = a[i];
    }

 
    long long w = 0;
    for (int i = 1; i <= n; i++) w ^= f[i];

    for (int i = n+1; i <= 3*n; i++) 
    {
        f[i] = w;           
        w ^= f[i];          
        w ^= f[i-n];        
    }


    s[1] = f[1];
    for (int i = 2; i <= 3*n; i++) 
    {
        s[i] = s[i-1] ^ f[i];
    }


    int ans = 0;
    for (int len = 1; len <= 2*n; len++) 
    {
        bool ok = true;
        for (int i = 1; i <= len; i++)
        {
            if (s[i] != s[i+len])
            {
                ok = false;
                break;
            }
        }
        if(ok)
        {
            ans = len;
            break;
        }
    }

    while (q--) 
    {
        long long k; cin >> k;
        long long idx = k % ans;
        if (idx == 0) idx = ans;
        cout << s[idx] << "\n";         
    }
    return 0;
}
