#include <bits/stdc++.h>
using namespace std;

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int t;
    cin >> t;
    while (t--) {
        int n;
        cin >> n;
        vector<int> p(n);
        for (int i = 0; i < n; i++) cin >> p[i];

        int pos1 = find(p.begin(), p.end(), 1) - p.begin();
        bool inc = true;
        for (int i = pos1; i < n; i++)
        {
            if (p[i] != i - pos1 + 1) { inc = false; break; }
        }
        for (int i = pos1 - 1; i >= 0; i--) {
            if (p[i] != p[i+1] + 1) { inc = false; break; }
        }

        int posn = find(p.begin(), p.end(), n) - p.begin();
        bool dec = true;
        for (int i = posn; i < n; i++) 
        {
            if (p[i] != n - (i - posn)) { dec = false; break; }
        }
        for (int i = posn - 1; i >= 0; i--) 
        {
            if (p[i] != p[i+1] - 1) { dec = false; break; }
        }

        cout << (inc || dec ? "YES\n" : "NO\n");
    }
}
