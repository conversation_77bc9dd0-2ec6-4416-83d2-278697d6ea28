#include <bits/stdc++.h>
using namespace std;

int ans = 0;
int n, l, r, x;
vector<int> v;

void backtrack(string s, int i, int sum, int maxVal, int minVal) {
    if (i == n) {
        int diff = (maxVal == INT_MIN || minVal == INT_MAX) ? 0 : abs(maxVal - minVal);
        if (diff >= x && sum >= l && sum <= r) {
            ans++;
        }
        return;
    }

    backtrack(s + "0", i + 1, sum, maxVal, minVal);


    int newMax = max(maxVal, v[i]);
    int newMin = min(minVal, v[i]);
    backtrack(s + "1", i + 1, sum + v[i], newMax, newMin);
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);
    cin >> n >> l >> r >> x;
    v.resize(n);
    for (int i = 0; i < n; ++i) cin >> v[i];

    backtrack("", 0, 0, INT_MIN, INT_MAX);
    cout << ans << endl;
    return 0;
}