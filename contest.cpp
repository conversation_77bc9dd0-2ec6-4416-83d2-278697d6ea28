#include <bits/stdc++.h>
using namespace std;

void init() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main() {
    init();
    int t;
    cin >> t;
    while (t--) {
        int n;
        cin >> n;
        vector<int> a(n);
        map<int,int> freq;
        for (int i = 0; i < n; i++) 
        {
            cin >> a[i];
            freq[a[i]]++;
        }

        int ans = 0;
        for (int k = 1; k <= n; k++) 
        {
            int cnt = 0;
            for (auto &p : freq) {
                if (p.second >= k) cnt++;
            }
            ans = max(ans, cnt * k);
        }

        cout << ans << "\n";
    }
    return 0;
}
