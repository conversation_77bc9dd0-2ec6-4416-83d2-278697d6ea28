#include <bits/stdc++.h>
using namespace std;

vector<int> primes;

void sieve(int limit = 1000000) 
{
    vector<bool> isPrime(limit+1, true);
    isPrime[0] = isPrime[1] = false;
    for (int i = 2; i * i <= limit; i++) {
        if (isPrime[i]) {
            for (int j = i*i; j <= limit; j += i)
                isPrime[j] = false;
        }
    }
    for (int i = 2; i <= limit; i++)
        if (isPrime[i]) primes.push_back(i);
}

int main() 
{
    ios::sync_with_stdio(0);
    cin.tie(0);

    sieve();

    int T;
    cin >> T;
    while (T--) 
    {
        unsigned long long N; 
        int K;
        cin >> N >> K;

        unsigned long long ans = 0;
        for (int i = 0; i + K <= (int)primes.size(); i++) 
        {
            unsigned long long prod = 1;
            bool ok = true;
            for (int j = 0; j < K; j++) 
            {
                int p = primes[i+j];
                if (prod > N / p) 
                { 
                    ok = false;
                    break;
                }
                prod *= p;
            }
            if (ok && prod <= N) 
            {
                ans = max(ans, prod);
            }
        }

        if (ans == 0) cout << -1 << "\n";
        else cout << ans << "\n";
    }

    return 0;
}
