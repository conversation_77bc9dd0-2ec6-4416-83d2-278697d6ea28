#include <bits/stdc++.h>
using namespace std;

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n;
    cin >> n;
    vector<pair<int,int>> a(n);

    for (int i = 0; i < n; i++) {
        int p, k;
        cin >> p >> k;
        a[i] = {k, p}; 
    }


    sort(a.begin(), a.end());

    vector<long long> dp(n+1, 0);

    for (int i = 1; i <= n; i++) 
    {
        int start = a[i-1].second;
        int end = a[i-1].first;
        int len = end - start;

        int j = upper_bound(a.begin(), a.end(), make_pair(start, INT_MAX)) - a.begin();
        j--; 

        dp[i] = max(dp[i-1], dp[j+1] + len);
    }

    cout << dp[n] << "\n";
    return 0;
}
