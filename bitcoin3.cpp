#include<bits/stdc++.h>
using namespace std;

#define int long long

const int N = 1e6 + 5;
const int SUM_K = 1e2 + 5;
int n , K , mod = 1e9 + 7;
int a[N];
int dp[3][3][SUM_K];

main()
{
    ios::sync_with_stdio(0);
    cin.tie(0);
    cin >> n >> K;
    for(int i = 1 ; i <= n ; i++)
        cin >> a[i];


    dp[0][0][0] = 0;
    dp[0][1][0] = -1e18;
    for(int j = 0 ; j <= 1 ; j++)
        for(int k = 1 ; k <= K ; k++)
            dp[0][j][k] = -1e18;


    for(int i = 1 ; i <= n ; i++)
    {
        int cur = i % 2;
        int prev = (i - 1) % 2;
	    for(int k = 0 ; k <= K ; k++)
	    {
    		dp[cur][0][k] = dp[prev][0][k];
    		if(k - 1 >= 0)
    		 	dp[cur][0][k] = max(dp[cur][0][k],
     				                dp[prev][1][k-1] + a[i]);
    		dp[cur][1][k] = max(dp[prev][1][k] , dp[prev][0][k] - a[i]);
	    }
    }

    int ans = -1e18;
    for(int i = 0 ; i <= K ; i++)
        ans = max({ans , dp[n % 2][0][i] , dp[n % 2][1][i]});

    cout << ans;




}