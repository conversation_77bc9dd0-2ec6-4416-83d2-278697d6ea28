#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}
void solve()
{
    int t;
    cin >> t;
    while(t--)
    {
        int n,m;
        cin >> n >> m;
        map<int,int> freq;
        vector<int> l(n+1);
        for(int i = 1; i <= m; i++)freq[i]++;
        for(int i = 0; i < n; i++) cin >> l[i];

    }
}
int main()
{
    init();
    return 0;
}