#include <bits/stdc++.h>
#define endl '\n'
const int maxn = 1e5;
const int mod = 1e9 + 7;
int dp[maxn+5][3];
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int n;
    cin >> n;
    dp[0][0] = 1;
    dp[1][0] = 2;
    dp[1][1] = 1;
    for(int i = 2; i <= n; i++)
    {
        for(int j = 0; j <= 1; j++)
        {
            dp[i][j] = (dp[i-1][j] + dp[i-2][j]) % mod;
            if(j - 1 >= 0) 
            {
                dp[i][j] =((dp[i][j] + dp[i-2][j-1]) % mod + dp[i-1][j-1]) % mod;
            }
        }
    }
    cout << (dp[n][0] + dp[n][1]) % mod;
    return 0;
}