#include <bits/stdc++.h>
using namespace std;

void init() {
    ios_base::sync_with_stdio(0);
    cin.tie(0);
}

int main() {
    init();
    int tc;
    cin >> tc;
    while (tc--) 
    {
        int n;
        cin >> n;
        vector<int> a(n);
        for (int i = 0; i < n; i++) cin >> a[i];

        int maxxx = 0;
        int maxx = a[0];
        for (int i = 1; i < n; i++) 
        {
            if (a[i] < maxx) 
            {
                maxxx = max(maxxx, maxx - a[i]);
            }
            maxx = max(maxx, a[i]);
        }

        int T = 0;
        while ((1LL << T) - 1 < maxxx) T++;

        cout << T << "\n";
    }
    return 0;
}
