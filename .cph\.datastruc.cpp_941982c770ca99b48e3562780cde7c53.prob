{"name": "Local: datastruc", "url": "d:\\C++\\colam\\datastruc.cpp", "tests": [{"id": 1758702573364, "input": "5\n5\n1 2 3 4 5\n01000\n7\n2 0\n2 1\n1 2 4\n2 0\n2 1\n1 1 3\n2 1\n6\n12 12 14 14 5 5\n001001\n3\n2 1\n1 2 4\n2 1\n4\n7 7 7 777\n1111\n3\n2 0\n1 2 3\n2 0\n2\n1000000000 996179179\n11\n1\n2 1\n5\n1 42 20 47 7\n00011\n5\n1 3 4\n1 1 1\n1 3 4\n1 2 4\n2 0\n", "output": "3 2 6 7 7 \n11 7 \n0 0 \n16430827 \n47 \n"}], "interactive": false, "memoryLimit": 1024, "timeLimit": 3000, "srcPath": "d:\\C++\\colam\\datastruc.cpp", "group": "local", "local": true}