#include <bits/stdc++.h>
#define endl '\n'
using namespace std;
const int limit = 1e7;
vector<bool> isPrime(limit+1, true);


void sieve() 
{
    isPrime[0] = isPrime[1] = false;
    for (int i = 2; i * i <= limit; i++) {
        if (isPrime[i]) {
            for (int j = i*i; j <= limit; j += i)
                isPrime[j] = false;
        }
    }
}



int tong(int n)
{
    int sum = 0;
    while (n != 0)
    {
        sum += n % 10;
        n /= 10;
    }
    return sum;
}
void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    sieve();


    int t;
    cin >> t;
    while(t--)
    {
        int l,r;
        cin >> l >> r;
        int ans = 0;
        for(int i = l; i <= r; i++)
        {
            if(isPrime[i] && tong(i) % 5 == 0)
            {
                ans++;
            }
        }
        cout << ans << endl;
    }
    return 0;
}