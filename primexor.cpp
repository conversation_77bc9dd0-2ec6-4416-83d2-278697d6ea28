#include <bits/stdc++.h>
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2)
        if (n % i == 0) return false;
    return true;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int T; 
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;
        long long Z = X ^ Y;

        
        long long A = -1, B = -1, C = -1;
        for (long long guess = 2; guess < (1 << 20); guess++) { 
 
            long long b = guess ^ X;
            long long c = guess ^ Z;
            if (isPrime(guess) && isPrime(b) && isPrime(c) 
                && guess != b && b != c && c != guess) {
                A = guess; B = b; C = c;
                break;
            }
        }

        vector<long long> res = {A, B, C};
        sort(res.begin(), res.end());
        cout << res[0] << " " << res[1] << " " << res[2] << "\n";
    }
}
