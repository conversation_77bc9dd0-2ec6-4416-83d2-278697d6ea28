#include <bits/stdc++.h>
using namespace std;

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n;
    cin >> n;
    vector<pair<int,int>> a(n);

    for (int i = 0; i < n; i++) {
        int p, k;
        cin >> p >> k;
        a[i] = {k, p}; 
    }


    sort(a.begin(), a.end());

    vector<long long> dp(n+1, 0);
    dp[0] = 0;
    for (int i = 1; i <= n; i++) 
    {
        int start = a[i].second;
        int end = a[i-1].first;
        if(start >= end) dp[i] = dp[i-1] + 1;
        else dp[i] = dp[i-1];
        
    }

    cout << dp[n] << "\n";
    return 0;
}
